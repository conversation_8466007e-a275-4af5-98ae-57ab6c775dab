using UnityEngine;
using System;
using FMODUnity;
using System.Collections;
using System.Collections.Generic;
using Random = UnityEngine.Random;

[Serializable]
public class FMODSoundPair
{
    public EventReference sound1;
    public EventReference sound2;
    [Range(0, 100)]
    public float spawnChance = 0f; // Chance in percent, set in inspector
}

public class SoulCreatureAudio : MonoBehaviour
{
    #region Settings
    [Header("Audio Settings")]
    [SerializeField] public List<FMODSoundPair> soundPairs = new List<FMODSoundPair>();
    [HideInInspector] public EventReference sound1;
    [HideInInspector] public EventReference sound2;

    [Header("Sound 1 Settings")]
    [SerializeField] private float minSound1Interval = 0.05f;
    [SerializeField] private float maxSound1Interval = 0.1f;
    [SerializeField] private float sound1EventsPerSecond = 5f;

    [Header("Performance Settings")]
    [SerializeField] private bool warnIfNoSoundPairs = true;
    [SerializeField] private float collisionCooldownMin = 0.5f;
    [SerializeField] private float collisionCooldownMax = 1.0f;
    #endregion

    #region Runtime Variables
    private Transform playerTransform;
    private ParticleSystem myParticleSystem;

    private List<ParticleCollisionEvent> collisionEvents;
    private ParticleSystem.Particle[] particlesArray;
    private float lastCollisionSoundTime = -Mathf.Infinity;
    private float lastSound1EventTime = -Mathf.Infinity;
    private bool hasSoundPairBeenSelected = false;
    private float nextSound1Time = 0f;
    private float oceanBottomY;
    private float currentCollisionCooldown = 0f; // Fixed cooldown duration
    #endregion

    [Header("Backpack Particles Control")]
    [Tooltip("If true, this creature will not give the player any backpack particles.")]
    public bool disableBackpackParticles = false;

    // Add this field to track the selected sound pair index
    public int SelectedSoundPairIndex { get; private set; } = 0;

    void Awake()
    {
        SelectRandomSoundPair();
    }

    void Start()
    {
        playerTransform = GameManager.Instance.player.transform;
        myParticleSystem = GetComponent<ParticleSystem>();
        oceanBottomY = GameManager.Instance.oceanBottom.position.y;

        if (myParticleSystem != null)
        {
            particlesArray = new ParticleSystem.Particle[myParticleSystem.main.maxParticles];
            collisionEvents = new List<ParticleCollisionEvent>();
        }

        nextSound1Time = Time.time + GetNextSound1Interval();
    }

    void Update()
    {
        if (playerTransform == null) return;

        // Prevent sound1 if player is beneath ocean bottom
        if (GameManager.Instance != null)
        {
            if (playerTransform.position.y < oceanBottomY)
                return;
        }

        float currentTime = Time.time;
        float minInterval = sound1EventsPerSecond > 0f ? 1f / sound1EventsPerSecond : 0.1f;

        if (!sound1.IsNull && currentTime >= nextSound1Time && (currentTime - lastSound1EventTime) >= minInterval)
        {
            Vector3 soundPos = transform.position;
            if (myParticleSystem != null && myParticleSystem.particleCount > 0)
            {
                if (particlesArray == null || particlesArray.Length < myParticleSystem.main.maxParticles)
                    particlesArray = new ParticleSystem.Particle[myParticleSystem.main.maxParticles];
                int particleCount = myParticleSystem.GetParticles(particlesArray);
                if (particleCount > 0)
                {
                    int idx = Random.Range(0, particleCount);
                    soundPos = particlesArray[idx].position;
                }
            }

            PlaySound1(soundPos);
            nextSound1Time = currentTime + GetNextSound1Interval();
        }
    }

    public EventReference Sound2 => sound2;

    public void SelectRandomSoundPair()
    {
        if (soundPairs != null && soundPairs.Count > 0)
        {
            float totalChance = 0f;
            foreach (var pair in soundPairs)
                totalChance += pair.spawnChance;
            if (totalChance <= 0f)
            {
                // fallback: pick random
                int randomIndex = Random.Range(0, soundPairs.Count);
                FMODSoundPair selectedPair = soundPairs[randomIndex];
                sound1 = selectedPair.sound1;
                sound2 = selectedPair.sound2;
                SelectedSoundPairIndex = randomIndex;
                hasSoundPairBeenSelected = true;
                return;
            }
            float rand = Random.Range(0f, totalChance);
            float cumulative = 0f;
            for (int i = 0; i < soundPairs.Count; i++)
            {
                cumulative += soundPairs[i].spawnChance;
                if (rand <= cumulative)
                {
                    sound1 = soundPairs[i].sound1;
                    sound2 = soundPairs[i].sound2;
                    SelectedSoundPairIndex = i;
                    hasSoundPairBeenSelected = true;
                    return;
                }
            }
            // fallback: pick last
            var lastPair = soundPairs[soundPairs.Count - 1];
            sound1 = lastPair.sound1;
            sound2 = lastPair.sound2;
            SelectedSoundPairIndex = soundPairs.Count - 1;
            hasSoundPairBeenSelected = true;
        }
        else if (warnIfNoSoundPairs && !hasSoundPairBeenSelected)
        {
            hasSoundPairBeenSelected = true;
        }
    }

    private float GetNextSound1Interval()
    {
        return Random.Range(minSound1Interval, maxSound1Interval);
    }

    protected virtual void ProcessCollisionAudio(GameObject other)
    {
        int numCollisionEvents = myParticleSystem.GetCollisionEvents(other, collisionEvents);
        if (numCollisionEvents > 0 && Time.time >= lastCollisionSoundTime + currentCollisionCooldown)
        {
            int randomIndex = Random.Range(0, numCollisionEvents);
            Vector3 collisionPos = collisionEvents[randomIndex].intersection;
            if (sound2.IsNull) SelectRandomSoundPair();
            if (!sound2.IsNull)
            {
                PlaySound2(collisionPos);
                lastCollisionSoundTime = Time.time;
                // Set new random cooldown for next collision
                currentCollisionCooldown = Random.Range(collisionCooldownMin, collisionCooldownMax);
            }
        }
    }

    protected virtual void PlaySound1(Vector3 position)
    {
        AudioManager.PlayEventInstance(sound1, position);
        lastSound1EventTime = Time.time;
    }

    protected virtual void PlaySound2(Vector3 position)
    {
        AudioManager.PlayEventInstanceBypassLOD(sound2, position);
        lastCollisionSoundTime = Time.time;
    }

    private void OnParticleCollision(GameObject other)
    {
        if (other.CompareTag("Player"))
        {
            if (myParticleSystem == null || playerTransform == null) return;
            ProcessCollisionAudio(other);
        }
    }
}